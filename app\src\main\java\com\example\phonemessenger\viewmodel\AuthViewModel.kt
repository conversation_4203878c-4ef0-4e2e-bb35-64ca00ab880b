package com.example.phonemessenger.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.phonemessenger.model.User
import com.example.phonemessenger.repository.AuthRepository
import com.google.firebase.auth.PhoneAuthCredential
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _authState = MutableLiveData<AuthState>()
    val authState: LiveData<AuthState> = _authState

    private val _user = MutableLiveData<User?>()
    val user: LiveData<User?> = _user

    init {
        checkAuthState()
    }

    private fun checkAuthState() {
        if (authRepository.isUserLoggedIn()) {
            _authState.value = AuthState.AUTHENTICATED
        } else {
            _authState.value = AuthState.UNAUTHENTICATED
        }
    }

    fun sendSignInLinkToEmail(email: String) {
        _authState.value = AuthState.LOADING

        viewModelScope.launch {
            try {
                val success = authRepository.sendSignInLinkToEmail(email)
                if (success) {
                    _authState.value = AuthState.EMAIL_SENT
                } else {
                    _authState.value = AuthState.ERROR
                }
            } catch (e: Exception) {
                _authState.value = AuthState.ERROR
            }
        }
    }

    fun signInWithEmailLink(email: String, emailLink: String) {
        _authState.value = AuthState.LOADING

        viewModelScope.launch {
            try {
                val user = authRepository.signInWithEmailLink(email, emailLink)
                _user.value = user
                _authState.value = AuthState.AUTHENTICATED
            } catch (e: Exception) {
                _authState.value = AuthState.ERROR
            }
        }
    }

    fun signInWithEmailAndPassword(email: String, password: String) {
        _authState.value = AuthState.LOADING

        viewModelScope.launch {
            try {
                val user = authRepository.signInWithEmailAndPassword(email, password)
                _user.value = user
                _authState.value = AuthState.AUTHENTICATED
            } catch (e: Exception) {
                _authState.value = AuthState.ERROR
            }
        }
    }

    fun createUserWithEmailAndPassword(email: String, password: String) {
        _authState.value = AuthState.LOADING

        viewModelScope.launch {
            try {
                val user = authRepository.createUserWithEmailAndPassword(email, password)
                _user.value = user
                _authState.value = AuthState.AUTHENTICATED
            } catch (e: Exception) {
                _authState.value = AuthState.ERROR
            }
        }
    }

    fun updateUserStatus(isOnline: Boolean) {
        viewModelScope.launch {
            authRepository.updateUserStatus(isOnline)
        }
    }

    fun signOut() {
        authRepository.signOut()
        _authState.value = AuthState.UNAUTHENTICATED
        _user.value = null
    }

    enum class AuthState {
        LOADING,
        AUTHENTICATED,
        UNAUTHENTICATED,
        EMAIL_SENT,
        ERROR
    }
}
