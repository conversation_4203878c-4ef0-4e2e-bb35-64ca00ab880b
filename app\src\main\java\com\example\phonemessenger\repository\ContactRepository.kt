package com.example.phonemessenger.repository

import com.example.phonemessenger.model.Contact
import com.example.phonemessenger.model.User
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ContactRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val authRepository: AuthRepository
) {

    fun getContacts(): Flow<List<Contact>> = flow {
        val userId = authRepository.getCurrentUserId() ?: return@flow

        val contactsSnapshot = firestore.collection("users")
            .document(userId)
            .collection("contacts")
            .get()
            .await()

        val contacts = contactsSnapshot.documents.mapNotNull { doc ->
            doc.toObject(Contact::class.java)
        }

        emit(contacts)
    }

    suspend fun addContact(email: String, displayName: String): Contact? {
        val userId = authRepository.getCurrentUserId() ?: return null

        // Find user by email
        val userQuery = firestore.collection("users")
            .whereEqualTo("email", email)
            .get()
            .await()

        val userDoc = userQuery.documents.firstOrNull() ?: return null
        val user = userDoc.toObject(User::class.java) ?: return null

        // Create contact
        val contact = Contact(
            id = userDoc.id,
            userId = user.id,
            email = email,
            displayName = displayName,
            photoUrl = user.photoUrl
        )

        // Save contact to user's contacts collection
        firestore.collection("users")
            .document(userId)
            .collection("contacts")
            .document(user.id)
            .set(contact)
            .await()

        return contact
    }

    suspend fun deleteContact(contactId: String) {
        val userId = authRepository.getCurrentUserId() ?: return

        firestore.collection("users")
            .document(userId)
            .collection("contacts")
            .document(contactId)
            .delete()
            .await()
    }
}
