package com.example.phonemessenger.repository

import android.net.Uri
import com.example.phonemessenger.model.Conversation
import com.example.phonemessenger.model.Message
import com.example.phonemessenger.model.MessageStatus
import com.example.phonemessenger.model.MessageType
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MessageRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val storage: FirebaseStorage,
    private val authRepository: AuthRepository
) {
    
    fun getConversations(): Flow<List<Conversation>> = flow {
        val userId = authRepository.getCurrentUserId() ?: return@flow
        
        val conversationsSnapshot = firestore.collection("conversations")
            .whereArrayContains("participants", userId)
            .orderBy("lastUpdated", Query.Direction.DESCENDING)
            .get()
            .await()
            
        val conversations = conversationsSnapshot.documents.mapNotNull { doc ->
            doc.toObject(Conversation::class.java)?.copy(id = doc.id)
        }
        
        emit(conversations)
    }
    
    fun getMessages(conversationId: String): Flow<List<Message>> = flow {
        val messagesSnapshot = firestore.collection("conversations")
            .document(conversationId)
            .collection("messages")
            .orderBy("timestamp", Query.Direction.ASCENDING)
            .get()
            .await()
            
        val messages = messagesSnapshot.documents.mapNotNull { doc ->
            doc.toObject(Message::class.java)?.copy(id = doc.id)
        }
        
        emit(messages)
    }
    
    suspend fun sendMessage(receiverId: String, content: String, type: MessageType = MessageType.TEXT, imageUri: Uri? = null): Message {
        val userId = authRepository.getCurrentUserId() ?: throw IllegalStateException("User not logged in")
        
        // Find or create conversation
        val conversationId = getOrCreateConversation(userId, receiverId)
        
        // Upload image if needed
        var mediaUrl = ""
        if (type == MessageType.IMAGE && imageUri != null) {
            val imageRef = storage.reference.child("images/${UUID.randomUUID()}")
            imageRef.putFile(imageUri).await()
            mediaUrl = imageRef.downloadUrl.await().toString()
        }
        
        // Create message
        val message = Message(
            senderId = userId,
            receiverId = receiverId,
            content = content,
            type = type,
            timestamp = System.currentTimeMillis(),
            status = MessageStatus.SENT,
            mediaUrl = mediaUrl
        )
        
        // Save message
        val messageRef = firestore.collection("conversations")
            .document(conversationId)
            .collection("messages")
            .document()
            
        val messageWithId = message.copy(id = messageRef.id)
        messageRef.set(messageWithId).await()
        
        // Update conversation with last message
        firestore.collection("conversations")
            .document(conversationId)
            .update(
                mapOf(
                    "lastMessage" to messageWithId,
                    "lastUpdated" to System.currentTimeMillis()
                )
            ).await()
            
        return messageWithId
    }
    
    suspend fun updateMessageStatus(messageId: String, conversationId: String, status: MessageStatus) {
        firestore.collection("conversations")
            .document(conversationId)
            .collection("messages")
            .document(messageId)
            .update("status", status)
            .await()
    }
    
    private suspend fun getOrCreateConversation(userId: String, receiverId: String): String {
        // Check if conversation exists
        val query = firestore.collection("conversations")
            .whereArrayContains("participants", userId)
            .get()
            .await()
            
        for (doc in query.documents) {
            val conversation = doc.toObject(Conversation::class.java)
            if (conversation?.participants?.contains(receiverId) == true) {
                return doc.id
            }
        }
        
        // Create new conversation
        val conversation = Conversation(
            participants = listOf(userId, receiverId),
            lastUpdated = System.currentTimeMillis()
        )
        
        val conversationRef = firestore.collection("conversations").document()
        conversationRef.set(conversation).await()
        
        return conversationRef.id
    }
}
