package com.example.phonemessenger.repository

import android.content.Context
import com.example.phonemessenger.model.User
import com.google.firebase.auth.ActionCodeSettings
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) {

    fun getCurrentUserId(): String? = auth.currentUser?.uid

    fun isUserLoggedIn(): Boolean = auth.currentUser != null

    suspend fun sendSignInLinkToEmail(email: String): Boolean {
        return try {
            val actionCodeSettings = ActionCodeSettings.newBuilder()
                .setUrl("https://phonemessenger.page.link/finishSignUp") // Replace with your domain
                .setHandleCodeInApp(true)
                .setAndroidPackageName(
                    "com.example.phonemessenger",
                    true, // installIfNotAvailable
                    "12" // minimumVersion
                )
                .build()

            auth.sendSignInLinkToEmail(email, actionCodeSettings).await()
            true
        } catch (e: Exception) {
            false
        }
    }

    suspend fun signInWithEmailLink(email: String, emailLink: String): User {
        val authResult = auth.signInWithEmailLink(email, emailLink).await()
        val user = authResult.user ?: throw IllegalStateException("Authentication failed")

        // Create or update user in Firestore
        val userModel = User(
            id = user.uid,
            email = user.email ?: "",
            displayName = user.displayName ?: user.email?.substringBefore("@") ?: "",
            photoUrl = user.photoUrl?.toString() ?: "",
            lastSeen = System.currentTimeMillis(),
            isOnline = true
        )

        firestore.collection("users").document(user.uid).set(userModel).await()

        return userModel
    }

    suspend fun signInWithEmailAndPassword(email: String, password: String): User {
        val authResult = auth.signInWithEmailAndPassword(email, password).await()
        val user = authResult.user ?: throw IllegalStateException("Authentication failed")

        // Update user status
        updateUserStatus(true)

        // Get user from Firestore
        val userDoc = firestore.collection("users").document(user.uid).get().await()
        return userDoc.toObject(User::class.java) ?: User(
            id = user.uid,
            email = user.email ?: "",
            displayName = user.displayName ?: user.email?.substringBefore("@") ?: "",
            photoUrl = user.photoUrl?.toString() ?: "",
            lastSeen = System.currentTimeMillis(),
            isOnline = true
        )
    }

    suspend fun createUserWithEmailAndPassword(email: String, password: String): User {
        val authResult = auth.createUserWithEmailAndPassword(email, password).await()
        val user = authResult.user ?: throw IllegalStateException("User creation failed")

        // Create user in Firestore
        val userModel = User(
            id = user.uid,
            email = user.email ?: "",
            displayName = user.email?.substringBefore("@") ?: "",
            photoUrl = user.photoUrl?.toString() ?: "",
            lastSeen = System.currentTimeMillis(),
            isOnline = true
        )

        firestore.collection("users").document(user.uid).set(userModel).await()

        return userModel
    }

    suspend fun updateUserStatus(isOnline: Boolean) {
        val userId = getCurrentUserId() ?: return

        firestore.collection("users").document(userId)
            .update(
                mapOf(
                    "isOnline" to isOnline,
                    "lastSeen" to System.currentTimeMillis()
                )
            ).await()
    }

    fun signOut() {
        auth.signOut()
    }
}
