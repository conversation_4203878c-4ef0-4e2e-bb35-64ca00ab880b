package com.example.phonemessenger.repository

import com.example.phonemessenger.model.User
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthOptions
import com.google.firebase.auth.PhoneAuthProvider
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) {
    
    fun getCurrentUserId(): String? = auth.currentUser?.uid
    
    fun isUserLoggedIn(): Boolean = auth.currentUser != null
    
    suspend fun signInWithPhoneAuthCredential(credential: PhoneAuthCredential): User {
        val authResult = auth.signInWithCredential(credential).await()
        val user = authResult.user ?: throw IllegalStateException("Authentication failed")
        
        // Create or update user in Firestore
        val userModel = User(
            id = user.uid,
            phoneNumber = user.phoneNumber ?: "",
            displayName = user.displayName ?: "",
            photoUrl = user.photoUrl?.toString() ?: "",
            lastSeen = System.currentTimeMillis(),
            isOnline = true
        )
        
        firestore.collection("users").document(user.uid).set(userModel).await()
        
        return userModel
    }
    
    suspend fun updateUserStatus(isOnline: Boolean) {
        val userId = getCurrentUserId() ?: return
        
        firestore.collection("users").document(userId)
            .update(
                mapOf(
                    "isOnline" to isOnline,
                    "lastSeen" to System.currentTimeMillis()
                )
            ).await()
    }
    
    fun signOut() {
        auth.signOut()
    }
}
