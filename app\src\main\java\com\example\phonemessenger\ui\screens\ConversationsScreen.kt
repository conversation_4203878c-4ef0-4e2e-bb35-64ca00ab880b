package com.example.phonemessenger.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.phonemessenger.model.Conversation
import com.example.phonemessenger.model.MessageType
import com.example.phonemessenger.viewmodel.MessageViewModel
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun ConversationsScreen(
    viewModel: MessageViewModel = hiltViewModel(),
    onConversationClick: (String, String) -> Unit,
    onContactsClick: () -> Unit,
    onSignOut: () -> Unit
) {
    val conversations by viewModel.conversations.observeAsState(emptyList())
    val messageState by viewModel.messageState.observeAsState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Messages") },
                actions = [
                    IconButton(onClick = onContactsClick) {
                        Icon(Icons.Default.Person, contentDescription = "Contacts")
                    },
                    IconButton(onClick = onSignOut) {
                        Icon(Icons.Default.ExitToApp, contentDescription = "Sign Out")
                    }
                ]
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            if (messageState == MessageViewModel.MessageState.LOADING) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else if (conversations.isEmpty()) {
                Text(
                    text = "No conversations yet. Start messaging by adding contacts.",
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(16.dp)
                )
            } else {
                LazyColumn {
                    items(conversations) { conversation ->
                        // In a real app, we would get the contact info for each conversation
                        // For this example, we'll use the first participant ID that is not the current user
                        val contactId = conversation.participants.firstOrNull() ?: ""
                        
                        ConversationItem(
                            conversation = conversation,
                            onClick = { onConversationClick(conversation.id, contactId) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ConversationItem(
    conversation: Conversation,
    onClick: () -> Unit
) {
    val dateFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable(onClick = onClick),
        elevation = 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // In a real app, we would load the contact's photo here
            
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp)
            ) {
                // In a real app, we would get the contact name
                Text(
                    text = "Contact Name",
                    style = MaterialTheme.typography.h6
                )
                
                conversation.lastMessage?.let { message ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val messagePreview = when (message.type) {
                            MessageType.IMAGE -> "Image"
                            else -> message.content
                        }
                        
                        Text(
                            text = messagePreview,
                            style = MaterialTheme.typography.body2,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = dateFormat.format(Date(message.timestamp)),
                            style = MaterialTheme.typography.caption
                        )
                    }
                }
            }
            
            if (conversation.unreadCount > 0) {
                Badge(
                    backgroundColor = MaterialTheme.colors.primary
                ) {
                    Text(
                        text = conversation.unreadCount.toString(),
                        color = MaterialTheme.colors.onPrimary
                    )
                }
            }
        }
    }
}
