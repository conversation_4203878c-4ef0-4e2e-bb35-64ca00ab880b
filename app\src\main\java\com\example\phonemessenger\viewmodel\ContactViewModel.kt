package com.example.phonemessenger.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.phonemessenger.model.Contact
import com.example.phonemessenger.repository.ContactRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ContactViewModel @Inject constructor(
    private val contactRepository: ContactRepository
) : ViewModel() {
    
    private val _contacts = MutableLiveData<List<Contact>>()
    val contacts: LiveData<List<Contact>> = _contacts
    
    private val _contactState = MutableLiveData<ContactState>()
    val contactState: LiveData<ContactState> = _contactState
    
    init {
        loadContacts()
    }
    
    private fun loadContacts() {
        _contactState.value = ContactState.LOADING
        
        viewModelScope.launch {
            try {
                contactRepository.getContacts().collect { contacts ->
                    _contacts.value = contacts
                    _contactState.value = ContactState.SUCCESS
                }
            } catch (e: Exception) {
                _contactState.value = ContactState.ERROR
            }
        }
    }
    
    fun addContact(phoneNumber: String, displayName: String) {
        _contactState.value = ContactState.LOADING
        
        viewModelScope.launch {
            try {
                val contact = contactRepository.addContact(phoneNumber, displayName)
                if (contact != null) {
                    loadContacts()
                } else {
                    _contactState.value = ContactState.ERROR
                }
            } catch (e: Exception) {
                _contactState.value = ContactState.ERROR
            }
        }
    }
    
    fun deleteContact(contactId: String) {
        viewModelScope.launch {
            try {
                contactRepository.deleteContact(contactId)
                loadContacts()
            } catch (e: Exception) {
                _contactState.value = ContactState.ERROR
            }
        }
    }
    
    enum class ContactState {
        LOADING,
        SUCCESS,
        ERROR
    }
}
