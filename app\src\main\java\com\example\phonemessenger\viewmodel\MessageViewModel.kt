package com.example.phonemessenger.viewmodel

import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.phonemessenger.model.Conversation
import com.example.phonemessenger.model.Message
import com.example.phonemessenger.model.MessageStatus
import com.example.phonemessenger.model.MessageType
import com.example.phonemessenger.repository.MessageRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MessageViewModel @Inject constructor(
    private val messageRepository: MessageRepository
) : ViewModel() {
    
    private val _conversations = MutableLiveData<List<Conversation>>()
    val conversations: LiveData<List<Conversation>> = _conversations
    
    private val _messages = MutableLiveData<List<Message>>()
    val messages: LiveData<List<Message>> = _messages
    
    private val _messageState = MutableLiveData<MessageState>()
    val messageState: LiveData<MessageState> = _messageState
    
    init {
        loadConversations()
    }
    
    private fun loadConversations() {
        _messageState.value = MessageState.LOADING
        
        viewModelScope.launch {
            try {
                messageRepository.getConversations().collect { conversations ->
                    _conversations.value = conversations
                    _messageState.value = MessageState.SUCCESS
                }
            } catch (e: Exception) {
                _messageState.value = MessageState.ERROR
            }
        }
    }
    
    fun loadMessages(conversationId: String) {
        _messageState.value = MessageState.LOADING
        
        viewModelScope.launch {
            try {
                messageRepository.getMessages(conversationId).collect { messages ->
                    _messages.value = messages
                    _messageState.value = MessageState.SUCCESS
                }
            } catch (e: Exception) {
                _messageState.value = MessageState.ERROR
            }
        }
    }
    
    fun sendTextMessage(receiverId: String, content: String) {
        _messageState.value = MessageState.SENDING
        
        viewModelScope.launch {
            try {
                messageRepository.sendMessage(receiverId, content)
                _messageState.value = MessageState.SUCCESS
            } catch (e: Exception) {
                _messageState.value = MessageState.ERROR
            }
        }
    }
    
    fun sendImageMessage(receiverId: String, imageUri: Uri) {
        _messageState.value = MessageState.SENDING
        
        viewModelScope.launch {
            try {
                messageRepository.sendMessage(
                    receiverId = receiverId,
                    content = "Image",
                    type = MessageType.IMAGE,
                    imageUri = imageUri
                )
                _messageState.value = MessageState.SUCCESS
            } catch (e: Exception) {
                _messageState.value = MessageState.ERROR
            }
        }
    }
    
    fun updateMessageStatus(messageId: String, conversationId: String, status: MessageStatus) {
        viewModelScope.launch {
            try {
                messageRepository.updateMessageStatus(messageId, conversationId, status)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    enum class MessageState {
        LOADING,
        SENDING,
        SUCCESS,
        ERROR
    }
}
