package com.example.phonemessenger.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.phonemessenger.R
import com.example.phonemessenger.viewmodel.AuthViewModel
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthProvider

@Composable
fun AuthScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onAuthSuccess: () -> Unit
) {
    val authState by viewModel.authState.observeAsState()
    
    if (authState == AuthViewModel.AuthState.AUTHENTICATED) {
        LaunchedEffect(key1 = authState) {
            onAuthSuccess()
        }
    }
    
    var phoneNumber by remember { mutableStateOf(TextFieldValue()) }
    var verificationCode by remember { mutableStateOf(TextFieldValue()) }
    var verificationId by remember { mutableStateOf("") }
    var isCodeSent by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(id = R.string.app_name),
            style = MaterialTheme.typography.h6,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        if (!isCodeSent) {
            // Phone number input
            OutlinedTextField(
                value = phoneNumber,
                onValueChange = { phoneNumber = it },
                label = { Text(stringResource(id = R.string.phone_number)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )
            
            Button(
                onClick = {
                    // In a real app, we would use PhoneAuthProvider to send verification code
                    // For this example, we'll simulate it
                    isCodeSent = true
                    verificationId = "sample-verification-id"
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(stringResource(id = R.string.send_code))
            }
        } else {
            // Verification code input
            OutlinedTextField(
                value = verificationCode,
                onValueChange = { verificationCode = it },
                label = { Text(stringResource(id = R.string.verification_code)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )
            
            Button(
                onClick = {
                    // In a real app, we would create a PhoneAuthCredential and sign in
                    // For this example, we'll simulate successful authentication
                    // val credential = PhoneAuthProvider.getCredential(verificationId, verificationCode.text)
                    // viewModel.signInWithPhoneAuthCredential(credential)
                    
                    // Simulate successful authentication
                    onAuthSuccess()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(stringResource(id = R.string.verify))
            }
        }
        
        if (authState == AuthViewModel.AuthState.LOADING) {
            CircularProgressIndicator(
                modifier = Modifier.padding(16.dp)
            )
        }
        
        if (authState == AuthViewModel.AuthState.ERROR) {
            Text(
                text = "Authentication failed. Please try again.",
                color = MaterialTheme.colors.error,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
}
