package com.example.phonemessenger.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.phonemessenger.R
import com.example.phonemessenger.viewmodel.AuthViewModel
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthProvider

@Composable
fun AuthScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onAuthSuccess: () -> Unit
) {
    val authState by viewModel.authState.observeAsState()

    if (authState == AuthViewModel.AuthState.AUTHENTICATED) {
        LaunchedEffect(key1 = authState) {
            onAuthSuccess()
        }
    }

    var email by remember { mutableStateOf(TextFieldValue()) }
    var password by remember { mutableStateOf(TextFieldValue()) }
    var isSignUp by remember { mutableStateOf(false) }
    var usePasswordlessLogin by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(id = R.string.app_name),
            style = MaterialTheme.typography.h6,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        // Email input
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email") },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        )

        if (!usePasswordlessLogin) {
            // Password input
            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("Password") },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )

            // Sign In/Sign Up Button
            Button(
                onClick = {
                    if (isSignUp) {
                        viewModel.createUserWithEmailAndPassword(email.text, password.text)
                    } else {
                        viewModel.signInWithEmailAndPassword(email.text, password.text)
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (isSignUp) "Sign Up" else "Sign In")
            }

            // Toggle between Sign In and Sign Up
            TextButton(
                onClick = { isSignUp = !isSignUp },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(if (isSignUp) "Already have an account? Sign In" else "Don't have an account? Sign Up")
            }

            // Passwordless login option
            TextButton(
                onClick = { usePasswordlessLogin = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Use passwordless email link")
            }
        } else {
            // Passwordless login
            Button(
                onClick = {
                    viewModel.sendSignInLinkToEmail(email.text)
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Send Sign-In Link")
            }

            TextButton(
                onClick = { usePasswordlessLogin = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Back to password login")
            }
        }

        if (authState == AuthViewModel.AuthState.LOADING) {
            CircularProgressIndicator(
                modifier = Modifier.padding(16.dp)
            )
        }

        if (authState == AuthViewModel.AuthState.EMAIL_SENT) {
            Text(
                text = "Sign-in link sent to your email. Please check your inbox.",
                color = MaterialTheme.colors.primary,
                modifier = Modifier.padding(top = 16.dp)
            )
        }

        if (authState == AuthViewModel.AuthState.ERROR) {
            Text(
                text = "Authentication failed. Please try again.",
                color = MaterialTheme.colors.error,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
}
