package com.example.phonemessenger

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.livedata.observeAsState
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.phonemessenger.ui.theme.PhoneMessengerTheme
import com.example.phonemessenger.ui.screens.AuthScreen
import com.example.phonemessenger.ui.screens.ContactsScreen
import com.example.phonemessenger.ui.screens.ConversationsScreen
import com.example.phonemessenger.ui.screens.ChatScreen
import com.example.phonemessenger.viewmodel.AuthViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            PhoneMessengerTheme {
                Surface(color = MaterialTheme.colors.background) {
                    PhoneMessengerApp()
                }
            }
        }
    }
}

@Composable
fun PhoneMessengerApp() {
    val navController = rememberNavController()
    val authViewModel: AuthViewModel = hiltViewModel()
    val authState = authViewModel.authState.observeAsState()
    
    NavHost(
        navController = navController,
        startDestination = when (authState.value) {
            AuthViewModel.AuthState.AUTHENTICATED -> "conversations"
            else -> "auth"
        }
    ) {
        composable("auth") {
            AuthScreen(
                onAuthSuccess = {
                    navController.navigate("conversations") {
                        popUpTo("auth") { inclusive = true }
                    }
                }
            )
        }
        
        composable("conversations") {
            ConversationsScreen(
                onConversationClick = { conversationId, contactId ->
                    navController.navigate("chat/$conversationId/$contactId")
                },
                onContactsClick = {
                    navController.navigate("contacts")
                },
                onSignOut = {
                    authViewModel.signOut()
                    navController.navigate("auth") {
                        popUpTo("conversations") { inclusive = true }
                    }
                }
            )
        }
        
        composable("contacts") {
            ContactsScreen(
                onContactClick = { contactId ->
                    navController.navigate("chat/new/$contactId")
                },
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
        
        composable("chat/{conversationId}/{contactId}") { backStackEntry ->
            val conversationId = backStackEntry.arguments?.getString("conversationId") ?: ""
            val contactId = backStackEntry.arguments?.getString("contactId") ?: ""
            
            ChatScreen(
                conversationId = conversationId,
                contactId = contactId,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
