package com.example.phonemessenger

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.livedata.observeAsState
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.phonemessenger.ui.theme.PhoneMessengerTheme
import com.example.phonemessenger.ui.screens.AuthScreen
import com.example.phonemessenger.ui.screens.ContactsScreen
import com.example.phonemessenger.ui.screens.ConversationsScreen
import com.example.phonemessenger.ui.screens.ChatScreen
import com.example.phonemessenger.viewmodel.AuthViewModel
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle email link authentication
        handleEmailLinkSignIn(intent)

        setContent {
            PhoneMessengerTheme {
                Surface(color = MaterialTheme.colors.background) {
                    PhoneMessengerApp()
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let { handleEmailLinkSignIn(it) }
    }

    private fun handleEmailLinkSignIn(intent: Intent) {
        val emailLink = intent.data?.toString()
        if (emailLink != null && FirebaseAuth.getInstance().isSignInWithEmailLink(emailLink)) {
            // The email link will be handled in the AuthScreen
            // You might want to store the email link in SharedPreferences or pass it to the AuthViewModel
        }
    }
}

@Composable
fun PhoneMessengerApp() {
    val navController = rememberNavController()
    val authViewModel: AuthViewModel = hiltViewModel()
    val authState = authViewModel.authState.observeAsState()

    NavHost(
        navController = navController,
        startDestination = when (authState.value) {
            AuthViewModel.AuthState.AUTHENTICATED -> "conversations"
            else -> "auth"
        }
    ) {
        composable("auth") {
            AuthScreen(
                onAuthSuccess = {
                    navController.navigate("conversations") {
                        popUpTo("auth") { inclusive = true }
                    }
                }
            )
        }

        composable("conversations") {
            ConversationsScreen(
                onConversationClick = { conversationId, contactId ->
                    navController.navigate("chat/$conversationId/$contactId")
                },
                onContactsClick = {
                    navController.navigate("contacts")
                },
                onSignOut = {
                    authViewModel.signOut()
                    navController.navigate("auth") {
                        popUpTo("conversations") { inclusive = true }
                    }
                }
            )
        }

        composable("contacts") {
            ContactsScreen(
                onContactClick = { contactId ->
                    navController.navigate("chat/new/$contactId")
                },
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }

        composable("chat/{conversationId}/{contactId}") { backStackEntry ->
            val conversationId = backStackEntry.arguments?.getString("conversationId") ?: ""
            val contactId = backStackEntry.arguments?.getString("contactId") ?: ""

            ChatScreen(
                conversationId = conversationId,
                contactId = contactId,
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
