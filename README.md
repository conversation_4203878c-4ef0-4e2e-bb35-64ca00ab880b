# Phone Messenger

A modern Android messaging application built with <PERSON><PERSON><PERSON> and Jetpack Compose.

## Features

- User authentication with email/password and passwordless email links
- Contact management
- One-to-one messaging
- Message status indicators (sent, delivered, read)
- Push notifications
- Image sharing

## Architecture

- **Frontend**: <PERSON><PERSON><PERSON>, Jetpack Compose
- **Backend**: Firebase (Authentication, Firestore, Storage, Cloud Messaging)
- **Architecture Pattern**: MVVM (Model-View-ViewModel)

## Key Libraries

- **Dagger/Hilt**: For dependency injection
- **Coroutines**: For asynchronous operations
- **Glide**: For image loading
- **Firebase**: For backend services

## Project Structure

- **model**: Data classes representing the domain entities
- **repository**: Classes responsible for data access
- **viewmodel**: ViewModels that handle the UI logic
- **ui**: Compose UI components and screens
- **service**: Background services for push notifications

## Setup Instructions

1. Clone the repository
2. Create a Firebase project and add your `google-services.json` file to the app directory
3. Enable Firebase Authentication, Firestore, Storage, and Cloud Messaging in your Firebase project
4. Build and run the application

## Development Plan

### Phase 1: Setup & Authentication (2 weeks)
- Project setup with Firebase
- User registration with email/password and passwordless email links
- Basic UI framework

### Phase 2: Core Messaging (3 weeks)
- Contact list implementation
- Message database structure
- Real-time messaging functionality
- Message status tracking

### Phase 3: UI/UX & Media (2 weeks)
- Polished conversation UI
- Image sharing capability
- Push notification system

### Phase 4: Testing & Launch (1 week)
- Bug fixes and performance optimization
- Play Store preparation
- Initial release
